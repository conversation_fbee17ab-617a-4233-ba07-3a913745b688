import 'package:flutter/material.dart';
import '../models/base_model.dart';

enum BookingStatus { pending, approved, cancelled } // Updated enum

enum BookingType { hotel, flight, villa, car, cruise }

class BookingHistoryModel extends BaseModel {
  final String id;
  final BookingType type;
  final BookingStatus status;
  final DateTime startDate;
  final DateTime endDate;
  final String location;
  final String imageUrl;
  final double price;
  final String? description;

  BookingHistoryModel({
    required this.id,
    required this.type,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.location,
    required this.imageUrl,
    required this.price,
    this.description,
  });

  factory BookingHistoryModel.fromJson(Map<String, dynamic> json) {
    // Helper to safely get a string, converting if necessary
    String _getString(dynamic value, {String defaultValue = ''}) {
      if (value == null) return defaultValue;
      return value.toString();
    }

    // Helper to parse status from int or string
    BookingStatus _parseStatus(dynamic statusValue) { // Removed startDateString parameter
      if (statusValue is int) {
        if (statusValue == 0) return BookingStatus.pending;   // API 0 -> Pending
        if (statusValue == 1) return BookingStatus.approved;  // API 1 -> Approved
        if (statusValue == 2) return BookingStatus.cancelled; // API 2 -> Cancelled
        return BookingStatus.pending; // Default for unknown integer status
      } else if (statusValue is String) {
        // Fallback for string status if API ever changes
        if (statusValue.toLowerCase() == 'pending') return BookingStatus.pending;
        if (statusValue.toLowerCase() == 'approved') return BookingStatus.approved;
        if (statusValue.toLowerCase() == 'cancelled') return BookingStatus.cancelled;
        return BookingStatus.values.firstWhere( // More generic string parsing
          (e) => e.toString().toLowerCase() == 'bookingstatus.${statusValue.toLowerCase()}',
          orElse: () => BookingStatus.pending,
        );
      }
      return BookingStatus.pending; // Default
    }

    // Helper to parse type
    BookingType _parseType(dynamic typeValue) {
      final String typeString = _getString(typeValue, defaultValue: 'villa'); // Default to villa if null/empty
      return BookingType.values.firstWhere(
        (e) => e.toString().toLowerCase() == 'bookingtype.${typeString.toLowerCase()}',
        orElse: () => BookingType.villa, // Default if API sends unknown or if it's a villa booking
      );
    }
    
    final villaData = json['villa'] as Map<String, dynamic>? ?? {};

    return BookingHistoryModel(
      id: _getString(json['id']),
      // Use 'bookingType' from API, default to 'villa' if null or not present
      type: _parseType(json['bookingType']),
      // Use 'status' from API, which is an int
      status: _parseStatus(json['status']), // Pass only status value
      // Use 'fromDate' and 'toDate' from API for dates
      startDate: DateTime.parse(_getString(json['fromDate'])),
      endDate: DateTime.parse(_getString(json['toDate'])),
      // Derive location, imageUrl, description from villa object
      location: _getString(villaData['name'], defaultValue: 'Unknown Location'),
      imageUrl: _getString(villaData['profilePicture'] ?? (villaData['images'] as List?)?.firstOrNull, defaultValue: ''), // Placeholder if no image
      price: (json['amount'] as num?)?.toDouble() ?? 0.0, // Use 'amount' from API
      description: _getString(villaData['desc'], defaultValue: ''),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'location': location,
      'imageUrl': imageUrl,
      'price': price,
      'description': description,
    };
  }

  String get formattedDateRange {
    final startMonth = _getMonthAbbreviation(startDate.month);
    final endMonth = _getMonthAbbreviation(endDate.month);

    if (startDate.month == endDate.month && startDate.year == endDate.year) {
      return '$startMonth ${startDate.day} - ${endDate.day}, ${startDate.year}';
    } else {
      return '$startMonth ${startDate.day} - $endMonth ${endDate.day}, ${endDate.year}';
    }
  }

  String _getMonthAbbreviation(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return months[month - 1];
  }

  String get typeDisplayName {
    switch (type) {
      case BookingType.hotel:
        return 'Hotel';
      case BookingType.flight:
        return 'Flight';
      case BookingType.villa:
        return 'Villa';
      case BookingType.car:
        return 'Car';
      case BookingType.cruise:
        return 'Cruise';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.approved:
        return 'Approved';
      case BookingStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color getStatusColor() {
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange; // Example color for Pending
      case BookingStatus.approved:
        return const Color(0xFF1E9D6A); // Green for Approved (was Upcoming)
      case BookingStatus.cancelled:
        return const Color(0xFFEB5757); // Red
    }
  }

  @override
  List<Object?> get props => [
    id,
    type,
    status,
    startDate,
    endDate,
    location,
    imageUrl,
    price,
    description,
  ];
}
